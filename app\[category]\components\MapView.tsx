'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { MAPBOX_CONFIG, isMapboxConfigured } from '@/lib/mapConfig'
import { Category, Recommendation } from '@/types/types'
import { createRoot } from 'react-dom/client'
import { Map, Marker, Popup } from 'mapbox-gl'
import { cn } from '@/lib/utils'
import RecommendationMapMarker from '@/app/[category]/components/RecommendationMapMarker'
import RecommendationMapPopup from '@/app/[category]/components/RecommendationMapPopup'
import MapOverlay from '@/app/[category]/components/MapOverlay'
import MapStatus from '@/app/[category]/components/MapStatus'

type Props = {
  recommendations: Recommendation[]
  category: Category
  gradient: string
}

const MapView = ({ recommendations, category, gradient }: Props) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<Map>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  const addMarkersToMap = useCallback(() => {
    if (!map.current) return

    try {
      recommendations.forEach(
        (
          {
            id,
            name,
            image,
            rating,
            category: recommendationCategory,
            address,
            hours,
            coordinates,
          },
          index
        ) => {
          const markerElement = document.createElement('div')
          const markerRoot = createRoot(markerElement)
          markerRoot.render(<RecommendationMapMarker gradient={gradient} index={index} />)

          const popupElement = document.createElement('div')
          const popupRoot = createRoot(popupElement)
          popupRoot.render(
            <RecommendationMapPopup
              category={category}
              gradient={gradient}
              id={id}
              name={name}
              image={image}
              rating={rating}
              recommendationCategory={recommendationCategory}
              address={address}
              hours={hours}
            />
          )

          const popup = new Popup({
            offset: 25,
            closeOnClick: true,
            closeOnMove: false,
            className: 'map-popup w-full',
          }).setDOMContent(popupElement)

          new Marker(markerElement).setLngLat(coordinates).setPopup(popup).addTo(map.current!)
        }
      )
    } catch {
      setIsError(true)
    }
  }, [category, gradient, recommendations])

  useEffect(() => {
    if (map.current || !mapContainer.current || !isMapboxConfigured()) {
      setIsError(true)
      return
    }

    const initializeMap = async () => {
      try {
        const mapboxgl = await import('mapbox-gl')

        mapboxgl.default.accessToken = MAPBOX_CONFIG.accessToken
        map.current = new mapboxgl.default.Map({
          container: mapContainer.current!,
          style: MAPBOX_CONFIG.style,
          center: MAPBOX_CONFIG.center,
          zoom: MAPBOX_CONFIG.zoom,
          attributionControl: false,
        })

        map.current.on('load', () => setMapLoaded(true))
        map.current.on('error', () => setIsError(true))
        map.current.addControl(new mapboxgl.default.NavigationControl(), 'top-right')
      } catch {
        setIsError(true)
      }
    }

    initializeMap()

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [addMarkersToMap])

  useEffect(() => {
    if (map.current && mapLoaded) map.current?.resize()
  }, [isFullscreen, mapLoaded])

  useEffect(() => {
    if (mapLoaded && map.current) addMarkersToMap()
  }, [recommendations, addMarkersToMap, mapLoaded])

  const toggleFullscreen = () => setIsFullscreen(prev => !prev)

  return (
    <section
      className={cn(
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : 'h-80 rounded-lg',
        'p-0 bg-slate-100 overflow-hidden shadow-lg transition-all duration-300'
      )}
    >
      <div
        ref={mapContainer}
        className={cn('relative size-full overflow-hidden', (!mapLoaded || isError) && 'hidden')}
      >
        {!isError && mapLoaded && (
          <MapOverlay
            recommendationsLength={recommendations.length}
            isFullscreen={isFullscreen}
            toggleFullscreen={toggleFullscreen}
          />
        )}
      </div>

      <MapStatus isError={isError} mapLoaded={mapLoaded} />
    </section>
  )
}

export default MapView
