'use client'

import { useState } from 'react'
import { Category, Recommendation } from '@/types/types'
import useRecommendationListType from '@/hooks/useRecommendationListType'
import IconBadge from '@/components/IconBadge'
import { Badge } from '@/components/UI/badge'
import { Sparkles } from 'lucide-react'
import RecommendationCard from '@/app/[category]/components/RecommendationCard'
import MapView from '@/app/[category]/components/MapView'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import { useRecommendationFilterStore } from '@/store/recommendationFilterStore.store'
import { searchRecommendations } from '@/lib/searchUtils'
import { Button } from '@/components/UI/button'
import GlassCard from '@/components/GlassCard'
import { defaultFilterState, getActiveFiltersCount } from '@/lib/filterUtils'

type Props = {
  category: Category
  recommendations: Recommendation[]
}

const RecommendationList = ({ category, recommendations }: Props) => {
  const searchQuery = useRecommendationFilterStore(state => state.searchQuery)
  const setSearchQuery = useRecommendationFilterStore(state => state.setSearchQuery)
  const filters = useRecommendationFilterStore(state => state.filters)
  const setFilters = useRecommendationFilterStore(state => state.setFilters)
  const [categoryDetails] = useState(() => getCategoryDetailsById(category))
  const { recommendationListType } = useRecommendationListType()
  const showMap = recommendationListType === 'list'

  const filteredRecommendations = searchRecommendations(recommendations, searchQuery)
  const activeFiltersCount = getActiveFiltersCount(filters)

  return (
    <section className="relative flex flex-col gap-6 py-4 max-sm:px-4 max-w-md mx-auto">
      {showMap && (
        <>
          <MapView
            category={category}
            recommendations={recommendations}
            gradient={categoryDetails.gradient}
          />
          <div className="flex justify-between *:duration-300 opacity-0 animate-fade-up-transform">
            <h3 className="text-xl font-semibold">Nearby Locations</h3>
            <Badge variant="card" className="rounded-md text-accent-text" size="sm">
              {recommendations.length} places
            </Badge>
          </div>
        </>
      )}
      {/* {(searchQuery || activeFiltersCount > 0) && ( */}
      {searchQuery && (
        <GlassCard className="flex-col items-center gap-3">
          <p className={`flex gap-2 items-center text-primary-text font-medium text-sm`}>
            {filteredRecommendations.length === 0
              ? 'No place match your criteria'
              : `${filteredRecommendations.length} place was found`}
          </p>
          {filteredRecommendations.length === 0 && (
            <p className={`text-sm text-secondary-text`}>
              Try adjusting your{' '}
              {searchQuery && activeFiltersCount > 0
                ? 'search or filters'
                : searchQuery
                  ? 'search terms'
                  : 'filters'}
            </p>
          )}
          <div className="flex items-center justify-center gap-2 flex-wrap">
            {searchQuery && (
              <div
                className={`flex text-accent-text bg-card-bg backdrop-blur-xl border-[1px] border-glass-border rounded-full px-3 py-1 shadow-lg`}
              >
                <span>Searching for ”</span>
                <span className="flex-1 font-medium line-clamp-1 break-all">{searchQuery}</span>
                <span>”</span>
              </div>
            )}
            {activeFiltersCount > 0 && (
              <Badge
                variant="outline"
                className={`text-xs bg-gradient-to-r ${categoryDetails.gradient} text-white border-transparent`}
              >
                {activeFiltersCount} filter{activeFiltersCount > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
          <div className="flex gap-2 justify-center">
            {searchQuery && (
              <Button
                variant="frostedGlass"
                size="sm"
                onClick={() => setSearchQuery('')}
                className={'duration-300'}
              >
                Clear search
              </Button>
            )}
            {activeFiltersCount > 0 && (
              <Button
                variant="frostedGlass"
                size="sm"
                onClick={() => setFilters(defaultFilterState)}
                className={'duration-300'}
              >
                Clear filters
              </Button>
            )}
          </div>
        </GlassCard>
      )}

      <div className="flex flex-col gap-4">
        {filteredRecommendations.map((recommendation, index) => (
          <RecommendationCard
            key={recommendation.id}
            categoryDetails={categoryDetails}
            {...recommendation}
            index={index}
            showDetails={!showMap}
          />
        ))}
      </div>
      <IconBadge
        Icon={Sparkles}
        variant="card"
        text="More discoveries coming soon"
        className="hoverActive:scale-105 duration-700 mx-auto"
        iconClassName="text-yellow-400"
        size="lg"
      />
    </section>
  )
}

export default RecommendationList
