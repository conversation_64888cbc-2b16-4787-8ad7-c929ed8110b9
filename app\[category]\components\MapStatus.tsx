type Props = {
  isError: boolean
  mapLoaded: boolean
}

const MapStatus = ({ isError, mapLoaded }: Props) => {
  if (!isError && mapLoaded) return null

  return (
    <div className="flex flex-col items-center justify-center gap-4 text-center size-full">
      {!mapLoaded && !isError && (
        <div className="size-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin" />
      )}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-slate-800">
          {isError ? 'Map currently unavailable' : 'Loading Map'}
        </h3>
        <p className="text-sm text-slate-600">
          {isError ? 'Please try again later.' : 'Preparing your local recommendations...'}
        </p>
      </div>
    </div>
  )
}

export default MapStatus
