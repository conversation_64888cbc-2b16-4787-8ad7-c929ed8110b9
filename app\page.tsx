import ThemeSwitch from '@/components/ThemeSwitch'
import Logo from '@/app/components/Logo'
import Taglines from '@/app/components/Taglines'
import StatusIndicator from '@/app/components/StatusIndicator'
import CategoryCardList from '@/app/components/CategoryCardList'
import HomeFooter from '@/app/components/HomeFooter'

const HomePage = () => (
  <div className="flex-1 flex flex-col items-center justify-center gap-5 py-10 max-sm:px-4">
    <main className="w-full max-w-sm space-y-5">
      <ThemeSwitch className="fixed top-4 right-4 z-50" size="sm" />
      <div className="flex flex-col items-center gap-5 opacity-0 animate-fade-up animation-duration-1000 **:transition-colors **:duration-300">
        <Logo />
        <Taglines />
        <StatusIndicator />
      </div>
      <CategoryCardList />
    </main>
    <HomeFooter />
  </div>
)

export default HomePage
