import { defaultFilterState } from '@/lib/filterUtils'
import { FilterState } from '@/types/types'
import { create } from 'zustand'

type RecommendationFilterStore = {
  isFilterDrawerOpen: boolean
  setIsFilterDrawerOpen: (isOpen: boolean) => void
  filters: FilterState
  setFilters: (filters: FilterState) => void
  searchQuery: string
  setSearchQuery: (query: string) => void
}

export const useRecommendationFilterStore = create<RecommendationFilterStore>(set => ({
  isFilterDrawerOpen: false,
  setIsFilterDrawerOpen: (isOpen: boolean) => set({ isFilterDrawerOpen: isOpen }),
  filters: defaultFilterState,
  setFilters: (filters: FilterState) => set({ filters }),
  searchQuery: '',
  setSearchQuery: (searchQuery: string) => set({ searchQuery }),
}))
