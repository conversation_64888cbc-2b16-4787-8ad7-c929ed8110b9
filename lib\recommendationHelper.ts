import { Car, Navigation } from 'lucide-react'
import { Category, Recommendation } from '@/types/types'
import times from 'lodash/times'

export const getStarRating = (rating: number): { filled: 'full' | 'half' | 'none' }[] =>
  times(5, i => ({
    filled: rating >= i + 1 ? 'full' : rating >= i + 0.5 ? 'half' : 'none',
  }))

export const getPriceLevels = (priceLevel: number) => times(4, i => ({ filled: i < priceLevel }))

export const getCurrentStatus = (
  openingHours: Recommendation['openingHours'],
  category: Category
): boolean | null => {
  if (!openingHours) return null

  const now = new Date()
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'short' })
  const todaySchedule = Object.entries(openingHours).find(([day]) => day.includes(currentDay))

  if (todaySchedule && todaySchedule[1] !== 'Closed') {
    if (category === 'do') {
      // TODO: implement a check for Do's formats
      return true
    } else {
      const [start, end] = todaySchedule[1].split('–')
      const startTime = new Date().setHours(
        parseInt(start.split(':')[0]),
        parseInt(start.split(':')[1]),
        0,
        0
      )
      const endTime = new Date().setHours(
        parseInt(end.split(':')[0]),
        parseInt(end.split(':')[1]),
        0,
        0
      )
      return now.getTime() >= startTime && now.getTime() <= endTime
    }
  }

  return false
}

export const getTravelInfo = (
  recommendedTravel: Recommendation['recommendedTravel'],
  travelTime: Recommendation['travelTime']
) => {
  if (!recommendedTravel || !travelTime) {
    return null
  }

  return {
    icon: recommendedTravel === 'car' ? Car : Navigation,
    text: `${travelTime} by ${recommendedTravel}`,
    method: recommendedTravel,
  }
}

export const getReviewerName = (reviewer: string | undefined | null) => {
  if (!reviewer || typeof reviewer !== 'string') {
    return 'Anonymous User'
  }
  return reviewer
}
