import { Star } from 'lucide-react'
import { cn } from '@/lib/utils'
import IconBadge from '@/components/IconBadge'
import { getTravelInfo } from '@/lib/recommendationHelper'
import { CategoryDetails, Recommendation } from '@/types/types'
import LikeButton from '@/components/LikeButton'

type RecommendationProps = Pick<
  Recommendation,
  'rating' | 'category' | 'recommendedTravel' | 'travelTime'
>

type Props = RecommendationProps & {
  icon: CategoryDetails['icon']
  gradient?: CategoryDetails['gradient']
  id?: Recommendation['id']
  showLikeButton?: boolean
}

const RecommendationImageOverlay = ({
  id,
  rating,
  category,
  recommendedTravel,
  travelTime,
  gradient,
  icon: CategoryIcon,
  showLikeButton = false,
}: Props) => {
  const { text, icon: TravelIcon } = getTravelInfo(recommendedTravel, travelTime) || {}

  return (
    <>
      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent group-hoverActive:from-black/50 transition-colors duration-500 pointer-events-none" />
      <div className="*:absolute *:backdrop-blur-md *:text-sm **:duration-300">
        <IconBadge
          text={category}
          Icon={CategoryIcon}
          variant="card"
          className={cn('top-3 left-3 bg-gradient-to-br text-white border-0', gradient)}
        />
        {text && TravelIcon && (
          <IconBadge text={text} Icon={TravelIcon} variant="card" className="top-3 right-3" />
        )}
        {showLikeButton && id && (
          <LikeButton recommendationId={id} className="bottom-3 right-3 border-transparent" />
        )}
        <IconBadge
          text={rating.toString()}
          Icon={Star}
          variant="card"
          className={cn('bottom-3', showLikeButton ? 'left-3' : 'right-3')}
          iconClassName="text-amber-400 fill-amber-400"
        />
      </div>
    </>
  )
}

export default RecommendationImageOverlay
