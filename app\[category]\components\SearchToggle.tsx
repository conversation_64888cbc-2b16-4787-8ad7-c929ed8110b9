import React, { useState, useRef, useEffect } from 'react'
import { Search, X } from 'lucide-react'
import { Input } from '@/components/UI/input'
import { cn } from '@/lib/utils'
import IconButton from '@/components/IconButton'
import { Category } from '@/types/types'
import { useRecommendationFilterStore } from '@/store/recommendationFilterStore.store'

type Props = {
  category: Category
}

const SearchToggle = ({ category }: Props) => {
  const searchQuery = useRecommendationFilterStore(state => state.searchQuery)
  const previousSearchQuery = useRef(searchQuery)
  const setSearchQuery = useRecommendationFilterStore(state => state.setSearchQuery)
  const [isSearchActive, setIsSearchActive] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (isSearchActive && inputRef.current) {
      const timer = setTimeout(() => {
        inputRef.current?.focus()
      }, 150)
      return () => clearTimeout(timer)
    }
  }, [isSearchActive])

  useEffect(() => {
    if (searchQuery && !isSearchActive) {
      setIsSearchActive(true)
    }
  }, [searchQuery, isSearchActive])

  useEffect(() => {
    if (!searchQuery.trim() && previousSearchQuery.current.trim()) {
      setIsSearchActive(false)
      previousSearchQuery.current = ''
    }
  }, [searchQuery, isSearchActive, isFocused])

  const handleSearchClick = () => {
    if (!isSearchActive) {
      setIsSearchActive(true)
    }
  }

  const handleClearSearch = () => {
    setSearchQuery('')
    previousSearchQuery.current = ''
    setIsSearchActive(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
    previousSearchQuery.current = e.target.value
  }

  const handleInputBlur = () => {
    setIsFocused(false)
    if (!searchQuery.trim()) {
      setIsSearchActive(false)
    }
  }

  const handleInputFocus = () => {
    setIsFocused(true)
  }

  useEffect(() => {
    setSearchQuery('')
    setIsSearchActive(false)
  }, [category, setSearchQuery])

  return (
    <div className="relative">
      {!isSearchActive ? (
        <IconButton
          size="sm"
          variant="frostedGlass"
          text="Search"
          Icon={Search}
          onClick={handleSearchClick}
          className="hoverActive:scale-105"
        />
      ) : (
        <div
          className={cn(
            'relative transition-all duration-500 ease-out',
            isSearchActive ? 'w-64' : 'w-auto'
          )}
        >
          <div
            className={cn(
              'relative bg-white/30 backdrop-blur-xl rounded-full shadow-lg transition-all duration-300',
              isFocused && 'shadow-xl scale-102 ring-2 ring-purple-400/50'
            )}
          >
            <Search
              size={16}
              className={cn(
                'transition-colors duration-300 absolute left-4 top-1/2 transform -translate-y-1/2 z-10',
                isFocused ? 'text-accent-text' : 'text-secondary-text'
              )}
            />
            <Input
              ref={inputRef}
              value={searchQuery}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              placeholder={`Search ${category} places...`}
              className="px-12 py-3 text-primary-text placeholder:text-secondary-text/60 !border-0 !ring-0 !outline-0 duration-300 text-base rounded-full"
            />
            {searchQuery && (
              <IconButton
                Icon={X}
                variant="ghost"
                size="sm"
                onClick={handleClearSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 size-8 p-0 rounded-full hover:!bg-white/30 duration-300 hover:scale-110"
              />
            )}
            <div
              className={cn(
                'absolute inset-0 rounded-full transition-all duration-300 pointer-events-none',
                isFocused && 'bg-gradient-to-t from-transparent via-white/5 to-white/15'
              )}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default SearchToggle
