import Link from 'next/link'
import { Button, type ButtonProps } from '@/components/UI/button'
import { isExternalURL } from '@/lib/isExternalURL'
import { LucideIcon } from 'lucide-react'
import IconWithText from '@/components/IconWithText'

type Props = ButtonProps & {
  Icon: LucideIcon
  text?: string
  iconSize?: number
  iconClassName?: string
  link?: string
}

const IconButton = ({
  text,
  Icon,
  link,
  variant = 'glass',
  iconSize,
  iconClassName,
  ...props
}: Props) => (
  <Button variant={variant} {...props} {...(link && { asChild: true })}>
    {link ? (
      <Link
        href={link}
        {...(isExternalURL(link) && { target: '_blank', rel: 'noopener noreferrer' })}
      >
        <IconWithText Icon={Icon} text={text} iconClassName={iconClassName} size={iconSize} />
      </Link>
    ) : (
      <IconWithText Icon={Icon} text={text} iconClassName={iconClassName} size={iconSize} />
    )}
  </Button>
)

export default IconButton
