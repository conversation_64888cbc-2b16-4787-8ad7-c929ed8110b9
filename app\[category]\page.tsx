import { notFound } from 'next/navigation'
import { mockRecommendations } from '@/data/mockData'
import RecommendationListHeader from '@/app/[category]/components/RecommendationListHeader'
import RecommendationList from '@/app/[category]/components/RecommendationList'
import CategoryHeader from '@/app/[category]/components/CategoryHeader'
import { categories, Category } from '@/types/types'
import RecommendationListTypeProvider from '@/components/RecommendationListTypeProvider'
import FilterDrawer from '@/app/[category]/components/FilterDrawer'

type Props = {
  params: Promise<{ category: string }>
}

const CategoryPage = async ({ params }: Props) => {
  const { category } = await params

  if (!categories.includes(category as Category)) {
    notFound()
  }

  const recommendations = mockRecommendations[category as Category]

  return (
    <RecommendationListTypeProvider>
      <CategoryHeader category={category as Category} recommendations={recommendations} />
      <main>
        <RecommendationListHeader
          category={category as Category}
          recommendations={recommendations}
        />
        <RecommendationList category={category as Category} recommendations={recommendations} />
      </main>
      <FilterDrawer recommendations={recommendations} category={category as Category} />
    </RecommendationListTypeProvider>
  )
}

export default CategoryPage
