'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { X, Filter, RotateCcw, Check } from 'lucide-react'
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/UI/drawer'
import { Button } from '@/components/UI/button'
import { Badge } from '@/components/UI/badge'
import { Slider } from '@/components/UI/slider'
import { Checkbox } from '@/components/UI/checkbox'
import { Separator } from '@/components/UI/separator'
import {
  getFilterOptions,
  getPriceLevelText,
  getActiveFiltersCount,
  getTravelTimeCategory,
  defaultFilterState,
} from '@/lib/filterUtils'
import { Category, Recommendation } from '@/types/types'
import { useRecommendationFilterStore } from '@/store/recommendationFilterStore.store'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import IconButton from '@/components/IconButton'

const drawerTitles = {
  eat: 'Food & Dining Filters',
  drink: 'Drinks & Nightlife Filters',
  do: 'Activities & Experiences Filters',
}

type Props = {
  recommendations: Recommendation[]
  category: Category
}

const FilterDrawer = ({ recommendations, category }: Props) => {
  const isFilterDrawerOpen = useRecommendationFilterStore(state => state.isFilterDrawerOpen)
  const setIsFilterDrawerOpen = useRecommendationFilterStore(state => state.setIsFilterDrawerOpen)
  const [filters, setFilters] = useState(() => useRecommendationFilterStore.getState().filters)
  const globalFilters = useRecommendationFilterStore(state => state.filters)
  const setGlobalFilters = useRecommendationFilterStore(state => state.setFilters)
  const [filterOptions] = useState(() => getFilterOptions(recommendations, category))
  const [{ gradient }] = useState(() => getCategoryDetailsById(category))
  const activeFiltersCount = getActiveFiltersCount(filters)

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilterState)
    setGlobalFilters(defaultFilterState)
  }, [setFilters, setGlobalFilters])

  useEffect(() => {
    handleResetFilters()
  }, [category, handleResetFilters])

  const handlePriceLevelChange = (priceLevel: number, checked: boolean) => {
    const newPriceLevels = checked
      ? [...filters.priceLevel, priceLevel]
      : filters.priceLevel.filter(p => p !== priceLevel)

    setFilters({
      ...filters,
      priceLevel: newPriceLevels,
    })
  }

  const handleRatingChange = (value: number[]) => {
    setFilters({
      ...filters,
      rating: value[0],
    })
  }

  const handleTagToggle = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag]

    setFilters({
      ...filters,
      tags: newTags,
    })
  }

  const handleAmenityToggle = (amenity: string) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity]

    setFilters({
      ...filters,
      amenities: newAmenities,
    })
  }

  const handleTravelTimeToggle = (time: string, checked: boolean) => {
    const newTravelTimes = checked
      ? [...filters.travelTime, time]
      : filters.travelTime.filter(t => t !== time)

    setFilters({
      ...filters,
      travelTime: newTravelTimes,
    })
  }

  const handleCategoryToggle = (cat: string, checked: boolean) => {
    const newCategories = checked
      ? [...filters.category, cat]
      : filters.category.filter(c => c !== cat)

    setFilters({
      ...filters,
      category: newCategories,
    })
  }

  const handleClose = () => {
    setFilters(globalFilters)
    setIsFilterDrawerOpen(false)
  }

  const handleApplyFilters = () => {
    setGlobalFilters(filters)
    setIsFilterDrawerOpen(false)
  }

  return (
    <Drawer open={isFilterDrawerOpen} onOpenChange={setIsFilterDrawerOpen} onClose={handleClose}>
      <DrawerContent
        className={`max-h-[85vh] bg-card-bg backdrop-blur-xl border border-glass-border`}
        thumbClassName="bg-primary-text"
      >
        <DrawerHeader
          className={`bg-card-bg backdrop-blur-xl border-b border-glass-border sticky top-0 z-10 mt-4`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className={`size-10 rounded-full bg-gradient-to-r ${gradient} flex items-center justify-center`}
              >
                <Filter size={20} color="white" />
              </div>
              <div>
                <DrawerTitle className={`text-primary-text text-lg`}>
                  {drawerTitles[category]}
                </DrawerTitle>
                <DrawerDescription className={`text-secondary-text text-sm text-left`}>
                  {activeFiltersCount > 0
                    ? `${activeFiltersCount} filter${activeFiltersCount > 1 ? 's' : ''} active`
                    : 'Customize your search'}
                </DrawerDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {activeFiltersCount > 0 && (
                <IconButton
                  Icon={RotateCcw}
                  text="Reset"
                  variant="glass"
                  size="sm"
                  onClick={handleResetFilters}
                  className="border-none shadow-none"
                />
              )}
              <IconButton
                Icon={X}
                variant="glass"
                size="sm"
                onClick={handleClose}
                className="border-none shadow-none p-2"
              />
            </div>
          </div>
        </DrawerHeader>

        {/* Filter Content */}
        <div className={`flex-1 overflow-y-auto p-6 space-y-8 bg-card-bg backdrop-blur-sm`}>
          {/* Price Level */}
          <div className="space-y-4">
            <h3 className={`text-base font-semibold text-primary-text flex items-center gap-2`}>
              💰 Price Range
              {filters.priceLevel.length > 0 && <DrawerBadge text={filters.priceLevel.length} />}
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {filterOptions.priceLevels.map(level => (
                <DrawerCheckbox
                  key={level}
                  value={level}
                  label={getPriceLevelText(level)}
                  array={filters.priceLevel}
                  onChange={handlePriceLevelChange}
                  gradient={gradient}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Rating */}
          <div className="space-y-4">
            <h3 className={`text-base font-semibold text-primary-text flex items-center gap-2`}>
              ⭐ Minimum Rating
              {filters.rating > 0 && <DrawerBadge text={`${filters.rating}+ stars`} />}
            </h3>
            <div className="space-y-3">
              <Slider
                value={[filters.rating]}
                onValueChange={handleRatingChange}
                max={5}
                min={0}
                step={0.5}
                className="w-full"
              />
              <div className={`flex justify-between text-xs text-secondary-text`}>
                <span>Any rating</span>
                <span>5+ stars</span>
              </div>
              {filters.rating > 0 && (
                <p className={`text-sm text-accent-text`}>
                  Showing places with {filters.rating}+ star rating
                </p>
              )}
            </div>
          </div>

          <Separator />

          {/* Tags */}
          <div className="space-y-4">
            <h3 className={`text-base font-semibold text-primary-text flex items-center gap-2`}>
              🏷️ Atmosphere & Style
              {filters.tags.length > 0 && <DrawerBadge text={filters.tags.length} />}
            </h3>
            <div className="flex flex-wrap gap-2">
              {filterOptions.tags.map(tag => (
                <Button
                  key={tag}
                  variant="card"
                  size="sm"
                  className={`capitalize text-xs px-2 py-0.5 h-auto duration-300 hover:scale-105 rounded-md text-secondary-text ${
                    filters.tags.includes(tag) &&
                    `bg-gradient-to-r transition-colors ${gradient} text-white border-x-0`
                  }`}
                  onClick={() => handleTagToggle(tag)}
                >
                  {filters.tags.includes(tag) && <Check size={12} />}
                  {tag}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Categories */}
          {filterOptions.categories.length > 1 && (
            <>
              <div className="space-y-4">
                <h3 className={`text-base font-semibold text-primary-text flex items-center gap-2`}>
                  📍 Place Type
                  {filters.category.length > 0 && <DrawerBadge text={filters.category.length} />}
                </h3>
                <div className="space-y-2">
                  {filterOptions.categories.map(cat => (
                    <DrawerCheckbox
                      key={cat}
                      value={cat}
                      label={cat}
                      array={filters.category}
                      onChange={handleCategoryToggle}
                      gradient={gradient}
                    />
                  ))}
                </div>
              </div>
              <Separator />
            </>
          )}

          {/* Travel Time */}
          <div className="space-y-4">
            <h3 className={`text-base font-semibold text-primary-text flex items-center gap-2`}>
              🚶 Travel Time
              {filters.travelTime.length > 0 && <DrawerBadge text={filters.travelTime.length} />}
            </h3>
            <div className="grid grid-cols-1 gap-2">
              {filterOptions.travelTimes.map(time => (
                <DrawerCheckbox
                  key={time}
                  value={time}
                  label={getTravelTimeCategory(time)}
                  array={filters.travelTime}
                  onChange={handleTravelTimeToggle}
                  gradient={gradient}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Amenities */}
          <div className="space-y-4">
            <h3 className={`text-base font-semibold text-primary-text flex items-center gap-2`}>
              ✨ Features & Amenities
              {filters.amenities.length > 0 && <DrawerBadge text={filters.amenities.length} />}
            </h3>
            <div className="flex flex-wrap gap-2">
              {filterOptions.amenities.map(amenity => (
                <Button
                  key={amenity}
                  variant="card"
                  size="sm"
                  className={`capitalize text-xs px-2 py-0.5 h-auto duration-300 hover:scale-105 rounded-md text-secondary-text ${
                    filters.amenities.includes(amenity) &&
                    `bg-gradient-to-r transition-colors ${gradient} text-white border-x-0`
                  }`}
                  onClick={() => handleAmenityToggle(amenity)}
                >
                  {filters.amenities.includes(amenity) && <Check size={12} />}
                  {amenity}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <DrawerFooter className="flex-row bg-card-bg backdrop-blur-xl border-t border-glass-border sticky bottom-0">
          <Button
            variant="frostedGlass"
            className="rounded-md flex-1 gap-4 duration-300"
            onClick={handleResetFilters}
            disabled={activeFiltersCount === 0}
          >
            <RotateCcw size={16} />
            Reset All
          </Button>
          <Button
            className={`rounded-md flex-1 gap-4 bg-gradient-to-r ${gradient} text-white border-transparent hover:opacity-90 duration-300`}
            onClick={handleApplyFilters}
          >
            <Filter size={16} />
            Apply Filters
            {activeFiltersCount > 0 && (
              <Badge variant="card" className="rounded-md border-glass-border" size="sm">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

const DrawerBadge = ({ text }: { text: string | number }) => (
  <Badge
    variant="outline"
    className="bg-slate-200 dark:bg-white/20 border-[1px] border-glass-border rounded-md"
    size="sm"
  >
    {text}
  </Badge>
)

type DrawerCheckboxProps<T> = {
  value: T
  label: string
  array: T[]
  onChange: (value: T, checked: boolean) => void
  gradient: string
}

const DrawerCheckbox = ({
  value,
  label,
  array,
  onChange,
  gradient,
}: DrawerCheckboxProps<typeof value>) => (
  <label
    className={`flex items-center gap-3 p-3 rounded-2xl border-[1px] border-glass-border transition-everything duration-300 cursor-pointer hover:scale-102 overflow-hidden ${
      array.includes(value)
        ? `bg-gradient-to-r ${gradient} border-x-0 shadow-lg text-white`
        : `bg-card-bg hover:hover:bg-card-hover text-primary-text`
    }`}
  >
    <Checkbox
      checked={array.includes(value)}
      onCheckedChange={checked => onChange(value, !!checked)}
      className="duration-300"
    />
    <span className={`text-sm font-medium`}>{label}</span>
  </label>
)

export default FilterDrawer
