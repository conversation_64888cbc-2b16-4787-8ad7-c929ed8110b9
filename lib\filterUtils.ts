import { Category, FilterState, Recommendation } from '@/types/types'

export const defaultFilterState: FilterState = {
  priceLevel: [],
  rating: 0,
  tags: [],
  amenities: [],
  travelTime: [],
  category: [],
}

/**
 * Get available filter options for a given category
 */
export function getFilterOptions(recommendations: Recommendation[], category: Category) {
  const allTags = new Set<string>()
  const allAmenities = new Set<string>()
  const allCategories = new Set<string>()
  const allTravelTimes = new Set<string>()
  const priceLevels = new Set<number>()

  recommendations.forEach(rec => {
    rec.tags.forEach(tag => allTags.add(tag))
    rec.amenities.forEach(amenity => allAmenities.add(amenity))
    allCategories.add(rec.category)
    allTravelTimes.add(rec.travelTime)
    priceLevels.add(rec.priceLevel)
  })

  // Category-specific popular tags
  // Todo set all tags in mockData
  const categoryTags = {
    eat: [
      'coffee',
      'pastries',
      'brunch',
      'wine',
      'vegan',
      'gluten-free',
      'cozy',
      'family-friendly',
    ],
    drink: ['wine', 'craft beer', 'cocktails', 'traditional', 'modern', 'cozy'],
    do: ['cultural', 'architecture', 'nature', 'historic', 'free', 'guided tour'],
  }

  return {
    tags: Array.from(allTags).filter(tag => categoryTags[category].includes(tag)),
    amenities: Array.from(allAmenities).slice(0, 8),
    categories: Array.from(allCategories),
    travelTimes: Array.from(allTravelTimes).sort(),
    priceLevels: Array.from(priceLevels).sort(),
    maxRating: 5,
  }
}

/**
 * Apply filters to recommendations
 */
export function applyFilters(
  recommendations: Recommendation[],
  filters: FilterState
): Recommendation[] {
  return recommendations.filter(rec => {
    // Price level filter
    if (filters.priceLevel.length > 0 && !filters.priceLevel.includes(rec.priceLevel)) {
      return false
    }

    // Rating filter
    if (filters.rating > 0 && rec.rating < filters.rating) {
      return false
    }

    // Tags filter
    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag =>
        rec.tags.some(recTag => recTag.toLowerCase().includes(tag.toLowerCase()))
      )
      if (!hasMatchingTag) return false
    }

    // Amenities filter
    if (filters.amenities.length > 0) {
      const hasMatchingAmenity = filters.amenities.some(amenity =>
        rec.amenities.some(recAmenity => recAmenity.toLowerCase().includes(amenity.toLowerCase()))
      )
      if (!hasMatchingAmenity) return false
    }

    // Travel time filter
    if (filters.travelTime.length > 0 && !filters.travelTime.includes(rec.travelTime)) {
      return false
    }

    // Category filter
    if (filters.category.length > 0 && !filters.category.includes(rec.category)) {
      return false
    }

    return true
  })
}

/**
 * Count active filters
 */
export function getActiveFiltersCount(filters: FilterState): number {
  let count = 0

  if (filters.priceLevel.length > 0) count++
  if (filters.rating > 0) count++
  if (filters.tags.length > 0) count++
  if (filters.amenities.length > 0) count++
  if (filters.travelTime.length > 0) count++
  if (filters.category.length > 0) count++

  return count
}

/**
 * Get price level display text
 */
export function getPriceLevelText(level: number): string {
  const priceMap = {
    0: 'Free',
    1: 'Budget (€)',
    2: 'Moderate (€€)',
    3: 'Premium (€€€)',
  }
  return priceMap[level as keyof typeof priceMap] || `€${'€'.repeat(level)}`
}

/**
 * Get travel time category
 */
export function getTravelTimeCategory(time: string): string {
  const minutes = parseInt(time.match(/\d+/)?.[0] || '0')
  if (minutes <= 5) return 'Very Close (≤5 min)'
  if (minutes <= 10) return 'Close (≤10 min)'
  if (minutes <= 15) return 'Moderate (≤15 min)'
  return 'Further (>15 min)'
}
