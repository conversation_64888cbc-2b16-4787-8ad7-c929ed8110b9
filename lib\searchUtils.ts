import { FilterState, Recommendation } from '@/types/types'
import { applyFilters } from './filterUtils'

/**
 * Search recommendations based on query string
 * Searches across name, description, tags, category, address, and menu highlights
 */
export function searchRecommendations(
  recommendations: Recommendation[],
  searchQuery: string
): Recommendation[] {
  if (!searchQuery.trim()) {
    return recommendations
  }

  const query = searchQuery.toLowerCase().trim()

  return recommendations.filter(
    ({ name, description, tags, category, address, menuHighlights, amenities }) => {
      if (name.toLowerCase().includes(query)) return true
      if (description.toLowerCase().includes(query)) return true
      if (tags.some(tag => tag.toLowerCase().includes(query))) return true
      if (category.toLowerCase().includes(query)) return true
      if (address.toLowerCase().includes(query)) return true
      if (menuHighlights?.some(highlight => highlight.toLowerCase().includes(query))) return true
      if (amenities?.some(amenity => amenity.toLowerCase().includes(query))) return true

      return false
    }
  )
}

/**
 * Get search suggestions based on query
 */
export function getSearchSuggestions(
  recommendations: Recommendation[],
  query: string,
  maxSuggestions: number = 5
): string[] {
  if (!query.trim()) return []

  const queryLower = query.toLowerCase()
  const suggestions = new Set<string>()

  recommendations.forEach(rec => {
    // Add matching names
    if (rec.name.toLowerCase().includes(queryLower)) {
      suggestions.add(rec.name)
    }

    // Add matching tags
    rec.tags.forEach(tag => {
      if (tag.toLowerCase().includes(queryLower)) {
        suggestions.add(tag)
      }
    })

    // Add matching categories
    if (rec.category.toLowerCase().includes(queryLower)) {
      suggestions.add(rec.category)
    }
  })

  return Array.from(suggestions).slice(0, maxSuggestions)
}

/**
 * Highlight search matches in text
 */
export function highlightSearchMatch(text: string, query: string): string {
  if (!query.trim()) return text

  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

/**
 * Sort search results by relevance
 */
export function sortSearchResults(
  recommendations: Recommendation[],
  searchQuery: string
): Recommendation[] {
  if (!searchQuery.trim()) {
    // Default sort by rating when no search
    return [...recommendations].sort((a, b) => b.rating - a.rating)
  }

  const query = searchQuery.toLowerCase().trim()

  return [...recommendations].sort((a, b) => {
    let scoreA = 0
    let scoreB = 0

    // Name matches get highest score
    if (a.name.toLowerCase().includes(query)) scoreA += 10
    if (b.name.toLowerCase().includes(query)) scoreB += 10

    // Exact name matches get even higher score
    if (a.name.toLowerCase() === query) scoreA += 20
    if (b.name.toLowerCase() === query) scoreB += 20

    // Tag matches get medium score
    const aTagMatches = a.tags.filter(tag => tag.toLowerCase().includes(query)).length
    const bTagMatches = b.tags.filter(tag => tag.toLowerCase().includes(query)).length
    scoreA += aTagMatches * 5
    scoreB += bTagMatches * 5

    // Description matches get lower score
    if (a.description.toLowerCase().includes(query)) scoreA += 3
    if (b.description.toLowerCase().includes(query)) scoreB += 3

    // Category matches get medium score
    if (a.category.toLowerCase().includes(query)) scoreA += 7
    if (b.category.toLowerCase().includes(query)) scoreB += 7

    // If scores are equal, sort by rating
    if (scoreA === scoreB) {
      return b.rating - a.rating
    }

    return scoreB - scoreA
  })
}

/**
 * Combined search and filter function
 */
export function searchAndFilterRecommendations(
  recommendations: Recommendation[],
  searchQuery: string,
  filters: FilterState
): Recommendation[] {
  // First apply filters
  let filtered = applyFilters(recommendations, filters)

  // Then apply search
  if (searchQuery.trim()) {
    filtered = searchRecommendations(filtered, searchQuery)
  }

  // Finally sort by relevance
  return sortSearchResults(filtered, searchQuery)
}
